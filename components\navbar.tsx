"use client";

import * as React from "react";
import { usePathname } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { Link as NextIntlLink } from '@/i18n/navigation';
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { Menu as NavbarMenu, MenuItem, NavItem } from "@/components/ui/navbar-menu";
import Image from "next/image";
import LanguageSwitcher from "@/components/LanguageSwitcher";

export default function Navbar() {
  const pathname = usePathname();
  const t = useTranslations('Navigation');
  const [scrolled, setScrolled] = React.useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  // Persist mobile menu state during language changes
  React.useEffect(() => {
    // Only run on client side to avoid hydration issues
    if (typeof window !== 'undefined') {
      const savedMenuState = sessionStorage.getItem('mobileMenuOpen');
      if (savedMenuState === 'true') {
        setMobileMenuOpen(true);
        // Clear the saved state after restoring it
        sessionStorage.removeItem('mobileMenuOpen');
      }
    }
  }, []);

  const handleMobileMenuToggle = () => {
    const newState = !mobileMenuOpen;
    setMobileMenuOpen(newState);
    // Only use sessionStorage on client side
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('mobileMenuOpen', newState.toString());
    }
  };
  const [activeItem, setActiveItem] = React.useState<string | null>(null);

  const logClick = (name: string, e: React.MouseEvent) => {
    console.groupCollapsed(`Navbar click: ${name}`);
    console.log('Event target:', e.target);
    console.log('Current target:', e.currentTarget);
    console.log('Bubbles:', e.bubbles);
    console.log('Default prevented:', e.defaultPrevented);
    console.log('Propagation stopped:', e.isPropagationStopped());
    console.log('Mobile menu open:', mobileMenuOpen);
    console.log('Pathname:', pathname);
    console.groupEnd();
  };

  // Handle scroll effect
  React.useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10); // Set scrolled to true if scrolled more than 10px
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll); // Cleanup listener
  }, []);

  // Lock body scroll when mobile menu is open
  React.useEffect(() => {
    if (mobileMenuOpen) {
      // Only lock body scroll on mobile, not on desktop
      if (window.innerWidth < 768) {
        document.body.style.overflow = 'hidden';
      }
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  const navigationItems = [
    { name: t('approach'), href: "/our-approach" },
    { name: t('about'), href: "/about" },
    { name: t('testimonials'), href: "/testimonials" },
    { name: t('faq'), href: "/faq" },
    { name: t('contact'), href: "/contact" }
  ];

  // Removed unused isAboutPage variable

  return (
    <nav
      className={cn(
        "fixed w-full z-50 transition-all duration-300 ease-in-out",
        scrolled
          ? "md:bg-purple-950/80 md:backdrop-blur-md bg-purple-950 shadow-lg border-b border-purple-800/30"
          : "md:bg-purple-950/60 md:backdrop-blur-sm bg-purple-950 border-b border-purple-800/20"
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 overflow-visible">
        <div className="flex justify-between items-center py-2">
          <div className="flex items-center gap-2">
            <NextIntlLink href="/" className="flex items-center gap-4">
              {/* Use a div with fixed size to contain the image */}
              <div
                className="relative transition-all duration-300 hover:scale-110 -my-4 hover:drop-shadow-[0_0_8px_rgba(236,72,153,0.5)]"
                style={{ width: '80px', height: '80px' }}
              >
                <Image
                  src="/logo/UpZera_logo_3-nobkgr.png"
                  alt="UpZera Logo"
                  fill
                  sizes="64px"
                  className="object-contain"
                  style={{ filter: 'drop-shadow(0 0 4px rgba(236, 72, 153, 0.3))' }}
                  priority
                />
              </div>
              <span
                className={cn(
                  "hidden md:inline text-2xl font-bold transition-all duration-300 hover:scale-105 cursor-pointer",
                  "bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent",
                  "hover:bg-gradient-to-r hover:from-pink-400 hover:to-purple-400"
                )}
              >
                UpZera
              </span>
            </NextIntlLink>

            {/* Mobile Menu Button - Visible only on mobile */}
            <button
              className="md:hidden p-2 rounded-md text-white hover:bg-white/10 focus:outline-none"
              onClick={handleMobileMenuToggle}
              aria-label="Toggle menu"
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Desktop Navigation - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-0">
            <NavbarMenu setActive={setActiveItem}>
              <MenuItem
                setActive={setActiveItem}
                active={activeItem}
                item={t('services')}
                isActive={pathname === "/website-development" || pathname === "/chatbot-integration"}
              >
                <div className="flex flex-col w-max gap-4 p-4">
                  <NextIntlLink href="/website-development" className="flex h-auto w-full select-none flex-row items-center rounded-md bg-gradient-to-r from-pink-600 to-purple-600 px-4 py-3 no-underline outline-none focus:shadow-md hover:brightness-110 transition-all duration-200">
                    <div className="flex flex-col">
                      <div className="mb-1 text-lg font-medium text-white">
                        {t('websiteDevelopment')}
                      </div>
                      <p className="text-sm leading-tight text-white/90">
                        {t('websiteDevelopmentDesc')}
                      </p>
                    </div>
                  </NextIntlLink>
                  <NextIntlLink href="/chatbot-integration" className="flex h-auto w-full select-none flex-row items-center rounded-md bg-gradient-to-r from-pink-600 to-purple-600 px-4 py-3 no-underline outline-none focus:shadow-md hover:brightness-110 transition-all duration-200">
                    <div className="flex flex-col">
                      <div className="mb-1 text-lg font-medium text-white">
                        {t('chatbotIntegration')}
                      </div>
                      <p className="text-sm leading-tight text-white/90">
                        {t('chatbotIntegrationDesc')}
                      </p>
                    </div>
                  </NextIntlLink>
                </div>
              </MenuItem>
            </NavbarMenu>

            {/* Regular navigation items without dropdowns */}
            {navigationItems.map((item) => (
              <NavItem
                key={item.name}
                item={item.name}
                href={item.href}
                isActive={pathname === item.href}
              />
            ))}

            {/* Language Switcher */}
            <div className="relative">
              <LanguageSwitcher />
            </div>
          </div>

          {/* Mobile Menu Button - Visible only on mobile */}

          {/* Mobile Menu Overlay */}
          <div
            className={cn(
              "fixed inset-0 z-50 bg-purple-950/95 md:hidden transition-all duration-300 ease-in-out transform overflow-y-auto",
              mobileMenuOpen
                ? "opacity-100 translate-y-0"
                : "opacity-0 -translate-y-full pointer-events-none"
            )}
            onClick={(e) => {
              console.log('Mobile menu overlay clicked', e);
              if (e.target === e.currentTarget) {
                setMobileMenuOpen(false);
              }
            }}
          >
            <div className="flex flex-col min-h-full w-full px-6 pb-20">
              <div className="flex justify-between items-center pt-4 mb-2">
                <h2 className={cn(
                  "text-3xl font-bold text-white transform transition-all duration-500 ease-out",
                  mobileMenuOpen ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-8"
                )}>
                  {t('menu')}
                </h2>
                <button
                  className="p-2 rounded-md text-white hover:bg-white/10 focus:outline-none"
                  onClick={() => {
                    setMobileMenuOpen(false);
                    sessionStorage.removeItem('mobileMenuOpen');
                  }}
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {/* Subtitle Text */}
              <p className={cn(
                "text-white/70 text-sm mb-6 transform transition-all duration-500 ease-out",
                mobileMenuOpen ? "opacity-100 translate-x-0 delay-75" : "opacity-0 -translate-x-8"
              )}>
                {t('menuSubtitle')}
              </p>

              {/* Mobile Navigation Items */}
              <div className="flex flex-col space-y-4">
                <NextIntlLink
                  href="/"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-50" : "opacity-0 -translate-x-8"
                  )}
                  onClick={() => {
                    setMobileMenuOpen(false);
                    sessionStorage.removeItem('mobileMenuOpen');
                  }}
                  aria-label="Navigate to Home"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('home')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>
                <NextIntlLink
                  href="/website-development"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out relative z-10",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-75" : "opacity-0 -translate-x-8"
                  )}
                  onClick={(e) => {
                    logClick('Website Development', e);
                    setMobileMenuOpen(false);
                    sessionStorage.removeItem('mobileMenuOpen');
                  }}
                  aria-label="Navigate to Website Development"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('websiteDevelopment')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>

                <div className={cn(
                  "absolute left-0 w-full h-8 overflow-hidden transition-all duration-500 pointer-events-none",
                  mobileMenuOpen ? "opacity-100" : "opacity-0"
                )} style={{ top: '0', zIndex: -1 }}>
                  <div className="w-full h-16 bg-gradient-to-r from-pink-500/30 to-purple-500/30 rounded-full transform translate-x-12 rotate-6 pointer-events-none"></div>
                </div>

                <NextIntlLink
                  href="/chatbot-integration"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out relative z-10",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-150" : "opacity-0 -translate-x-8"
                  )}
                  onClick={(e) => {
                    logClick('Chatbot Integration', e);
                    setMobileMenuOpen(false);
                    sessionStorage.removeItem('mobileMenuOpen');
                  }}
                  aria-label="Navigate to Chatbot Integration"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('chatbotIntegration')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>

                <div className={cn(
                  "relative transform transition-all duration-300 ease-out",
                  mobileMenuOpen ? "opacity-100 translate-x-0 delay-200" : "opacity-0 -translate-x-8"
                )}>
                  <div className="absolute left-0 w-full h-16 overflow-hidden pointer-events-none" style={{ top: '0', zIndex: -1 }}>
                    <div className="w-full h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full transform -translate-x-16 rotate-6 opacity-20 pointer-events-none"></div>
                  </div>

                  <div className="pb-4 relative z-10">
                    <p className="text-white/70 text-xs mb-1">Concept & Ideation</p>
                    <NextIntlLink
                      href="/our-approach"
                      className="block w-full text-xl font-medium text-white hover:bg-pink-700 focus:bg-pink-700 bg-pink-600 rounded-lg py-3 px-4 flex items-center justify-between shadow-lg shadow-pink-600/20"
                      onClick={(e) => {
                        logClick('Approach', e); // Keep log for debugging if needed
                        setMobileMenuOpen(false); // Close menu on click
                        sessionStorage.removeItem('mobileMenuOpen');
                        // Allow default Link behavior to handle navigation
                      }}
                      aria-label="Navigate to Our Approach"
                      role="menuitem"
                    >
                      <span className="w-full h-full">{t('approach')}</span>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </NextIntlLink>
                    <p className="text-white/70 text-xs mt-1 px-1">
                      {t('conceptDescription')}
                    </p>
                  </div>
                </div>

                <NextIntlLink
                  href="/about"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out relative z-10",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-300" : "opacity-0 -translate-x-8"
                  )}
                  onClick={(e) => {
                    logClick('About Us', e); // Keep log for debugging if needed
                    setMobileMenuOpen(false); // Close menu on click
                    sessionStorage.removeItem('mobileMenuOpen');
                    // Allow default Link behavior to handle navigation
                  }}
                  aria-label="Navigate to About Us"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('about')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>

                <NextIntlLink
                  href="/testimonials"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out relative z-10",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-\\[400ms\\]" : "opacity-0 -translate-x-8"
                  )}
                  onClick={(e) => {
                    logClick('Testimonials', e); // Keep log for debugging if needed
                    setMobileMenuOpen(false); // Close menu on click
                    sessionStorage.removeItem('mobileMenuOpen');
                    // Allow default Link behavior to handle navigation
                  }}
                  aria-label="Navigate to Testimonials"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('testimonials')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>

                {/* FAQ Link */}
                <NextIntlLink
                  href="/faq"
                  className={cn(
                    "block w-full text-xl font-medium text-white hover:bg-white/10 focus:bg-white/10 bg-purple-900 rounded-lg py-3 px-4 flex items-center justify-between transform transition-all duration-300 ease-out relative z-10",
                    mobileMenuOpen ? "opacity-100 translate-x-0 delay-450" : "opacity-0 -translate-x-8"
                  )}
                  onClick={(e) => {
                    logClick('FAQ', e);
                    setMobileMenuOpen(false);
                    sessionStorage.removeItem('mobileMenuOpen');
                  }}
                  aria-label="Navigate to FAQ"
                  role="menuitem"
                >
                  <span className="w-full h-full">{t('faq')}</span>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </NextIntlLink>

                <div className={cn(
                  "relative transform transition-all duration-300 ease-out",
                  mobileMenuOpen ? "opacity-100 translate-x-0 delay-500" : "opacity-0 -translate-x-8"
                )}>
                  {/* Background decoration */}
                  <div className="absolute left-0 w-full h-16 overflow-hidden pointer-events-none" style={{ top: '-30px', zIndex: 0 }}>
                    <div className="w-full h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full transform -translate-y-8 translate-x-24 rotate-6 opacity-20 pointer-events-none"></div>
                  </div>

                  {/* Content */}
                  <div className="pb-4 relative z-10">
                    <p className="text-white/70 text-xs mb-1">{t('projectKickoff')}</p>
                    <NextIntlLink
                      href="/contact"
                      className="block w-full text-xl font-medium text-white hover:bg-pink-700 focus:bg-pink-700 bg-pink-600 rounded-lg py-3 px-4 flex items-center justify-between"
                      onClick={(e) => {
                        logClick('Contact Us', e); // Keep log for debugging if needed
                        setMobileMenuOpen(false); // Close menu on click
                        sessionStorage.removeItem('mobileMenuOpen');
                        // Allow default Link behavior to handle navigation
                      }}
                      aria-label="Navigate to Contact Us"
                      role="menuitem"
                    >
                      <span className="w-full h-full">{t('contact')}</span>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </NextIntlLink>

                  </div>
                </div>

                {/* Language Switcher in Mobile Menu */}
                <div className={cn(
                  "mt-6 transform transition-all duration-300 ease-out",
                  mobileMenuOpen ? "opacity-100 translate-x-0 delay-600" : "opacity-0 -translate-x-8"
                )}>
                  <div className="flex justify-center">
                    <LanguageSwitcher keepMenuOpen={true} />
                  </div>
                </div>
              </div>

              {/* Removed Contact Button in Mobile Menu */}
            </div>
          </div>

          <NextIntlLink href="/contact" onClick={(e) => {
            logClick('Free Consultation (Desktop)', e);
          }}>
            <Button
              className="px-2 py-0.5 text-xs md:px-3 md:py-1 md:text-xs bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105 relative z-10 flex justify-center items-center"
            >
              <span className="text-center">{t('freeConsultation')}</span>
            </Button>
          </NextIntlLink>
        </div>
      </div>
    </nav>
  );
}
